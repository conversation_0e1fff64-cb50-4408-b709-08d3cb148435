using Quartz.Impl;
using Quartz;
using System.Linq;
using Quartz.Logging;
using NLog;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MaoYouJi
{

  public class QuartzScheduler : Singleton<QuartzScheduler>, ISingletonAwake
  {
    IScheduler scheduler;

    public void Awake()
    {
      // 配置 Quartz 日志
      ConfigureQuartzLogging();

      Init().Coroutine();
    }

    private void ConfigureQuartzLogging()
    {
      // 确保 NLog 配置已加载
      if (LogManager.Configuration == null)
      {
        LogManager.Configuration = new NLog.Config.XmlLoggingConfiguration("../Config/NLog/NLog.config");
      }
      // 使用 NLog 作为 Quartz 的日志提供程序
      LogProvider.SetCurrentLogProvider(new QuartzNLogProvider());
    }

    private async ETTask Init()
    {
      // 创建调度器
      ISchedulerFactory schedulerFactory = new StdSchedulerFactory();
      scheduler = await schedulerFactory.GetScheduler();

      // 启动调度器
      await scheduler.Start();
      ETLog.Info("QuartzScheduler start");
      // Scene root = FiberManager.Instance.Get(ConstFiberId.Global).Root;

      // AddScheduleJob(new MaoScheduleJobInfo(root.GetActorId(), new InnerGlobalTimerPer_1_MinMsg()), "1", "1", 10000, 1000, 19);
    }

    /** 增加一次性定时任务，指定延迟后执行 */
    public void AddScheduleJob(MaoScheduleJobInfo jobInfo, string jobGroup, string jobId, long timeDelay)
    {
      InnerAddScheduleJob(jobInfo, jobGroup, jobId, TimeInfo.Instance.DelayDateTime(timeDelay), 0, 0).Coroutine();
    }

    /** 增加一次性定时任务，指定时间执行 */
    public void AddScheduleJob(MaoScheduleJobInfo jobInfo, string jobGroup, string jobId, DateTime date)
    {
      InnerAddScheduleJob(jobInfo, jobGroup, jobId, date, 0, 0).Coroutine();
    }

    /** 增加循环类型定时任务，指定延迟后循环执行 */
    public void AddScheduleJob(MaoScheduleJobInfo jobInfo, string jobGroup, string jobId, long timeDelay, long interval, int nums)
    {
      InnerAddScheduleJob(jobInfo, jobGroup, jobId, TimeInfo.Instance.DelayDateTime(timeDelay), interval, nums).Coroutine();
    }

    /** 增加循环类型定时任务，指定时间开始循环执行 */
    public void AddScheduleJob(MaoScheduleJobInfo jobInfo, string jobGroup, string jobId, DateTime startTime, long interval, int nums)
    {
      InnerAddScheduleJob(jobInfo, jobGroup, jobId, startTime, interval, nums).Coroutine();
    }

    private async ETTask InnerAddScheduleJob(MaoScheduleJobInfo jobInfo, string jobGroup, string jobId, DateTime startTime, long interverl, int nums = 0)
    {
      IJobDetail jobDetail = JobBuilder.Create<MaoScheduleJob>()
        .WithIdentity(jobId, jobGroup)
        .Build();
      jobDetail.JobDataMap.Put("message", jobInfo);

      TriggerBuilder triggerBuilder = TriggerBuilder.Create()
        .WithIdentity(jobId, jobGroup)
        .StartAt(startTime);

      if (nums > 0)
      {
        triggerBuilder.WithSimpleSchedule(x => x
          .WithInterval(TimeSpan.FromMilliseconds(interverl))
          .WithRepeatCount(nums)
        );
      }
      else if (nums == -1)
      {
        triggerBuilder.WithSimpleSchedule(x => x
          .WithInterval(TimeSpan.FromMilliseconds(interverl))
          .RepeatForever()
        );
      }
      ITrigger trigger = triggerBuilder.Build();
      try
      {
        await scheduler.ScheduleJob(jobDetail, trigger);
      }
      catch (Exception e)
      {
        // 这里永远都不应该报错的，如果报错了，说明调度器没有启动，必须记录日志
        ETLog.Error(e.Message);
      }
    }

    public void DeleteJob(string jobGroup, params string[] jobIds)
    {
      scheduler.DeleteJobs(jobIds.Select(id => new JobKey(id, jobGroup)).ToList());
    }
  }

  // 自定义 Quartz 日志提供程序
  [EnableClass]
  public class QuartzNLogProvider : ILogProvider
  {
    [StaticField]
    private static readonly Dictionary<string, NLog.Logger> LoggerCache = new Dictionary<string, NLog.Logger>();

    public Quartz.Logging.Logger GetLogger(string name)
    {
      if (!LoggerCache.TryGetValue(name, out var logger))
      {
        logger = LogManager.GetLogger(name);
        LoggerCache[name] = logger;
      }

      return (level, func, exception, parameters) =>
      {
        if (func == null) return true;

        string message = func();
        if (parameters != null && parameters.Length > 0)
        {
          message = string.Format(message, parameters);
        }

        switch (level)
        {
          case Quartz.Logging.LogLevel.Trace:
            logger.Trace(exception, message);
            break;
          case Quartz.Logging.LogLevel.Debug:
            logger.Debug(exception, message);
            break;
          case Quartz.Logging.LogLevel.Info:
            logger.Info(exception, message);
            break;
          case Quartz.Logging.LogLevel.Warn:
            logger.Warn(exception, message);
            break;
          case Quartz.Logging.LogLevel.Error:
            logger.Error(exception, message);
            break;
          case Quartz.Logging.LogLevel.Fatal:
            logger.Fatal(exception, message);
            break;
        }

        return true;
      };
    }

    public IDisposable OpenNestedContext(string message)
    {
      return NLog.NestedDiagnosticsLogicalContext.Push(message);
    }

    public IDisposable OpenMappedContext(string key, object value, bool destructure = false)
    {
      return NLog.MappedDiagnosticsLogicalContext.SetScoped(key, value);
    }
  }
}