using System.Collections.Generic;
using MemoryPack;

namespace MaoYouJi
{
  // 关系内网消息
  [MemoryPackable]
  [Message(MaoInnerMsgRange.InnerInitUserFriendInfoMsg)]
  public partial class InnerInitUserFriendInfoMsg : MaoYouMessage, IMessage
  {
  }

  [MemoryPackable]
  [Message(MaoInnerMsgRange.InnerUnInitUserFriendInfoMsg)]
  public partial class InnerUnInitUserFriendInfoMsg : MaoYouMessage, IMessage
  {
    public List<long> UserFriendInfoIds { get; set; }
  }

  // 活动内网消息
  [MemoryPackable]
  [Message(MaoInnerMsgRange.InnerUserQuitDaTaoSha)]
  public partial class InnerUserQuitDaTaoSha : MaoYouMessage, IMessage
  {
    public long targetUserId;
    public FightInfo killedSrc = null;
    public bool isBoom = false;
  }

  // 全局内网消息
  [MemoryPackable]
  [Message(MaoInnerMsgRange.InnerGlobalTimerPer_1_MinMsg)]
  public partial class InnerGlobalTimerPer_1_MinMsg : MaoYouMessage, IGlobalMessage
  {
  }

  [MemoryPackable]
  [Message(MaoInnerMsgRange.InnerGlobalTimerPer_5_MinMsg)]
  public partial class InnerGlobalTimerPer_5_MinMsg : MaoYouMessage, IGlobalMessage
  {
  }

  [MemoryPackable]
  [Message(MaoInnerMsgRange.InnerGlobalTimerPer_10_MinMsg)]
  public partial class InnerGlobalTimerPer_10_MinMsg : MaoYouMessage, IGlobalMessage
  {
  }

  [MemoryPackable]
  [Message(MaoInnerMsgRange.InnerGlobalTimerPer_30_MinMsg)]
  public partial class InnerGlobalTimerPer_30_MinMsg : MaoYouMessage, IGlobalMessage
  {
  }

  [MemoryPackable]
  [Message(MaoInnerMsgRange.InnerGlobalTimerPer_1_HourMsg)]
  public partial class InnerGlobalTimerPer_1_HourMsg : MaoYouMessage, IGlobalMessage
  {
  }

  [MemoryPackable]
  [Message(MaoInnerMsgRange.InnerGlobalTimerPer_1_DayMsg)]
  public partial class InnerGlobalTimerPer_1_DayMsg : MaoYouMessage, IGlobalMessage
  {
  }

  // 战斗内网消息
  [MemoryPackable]
  [Message(MaoInnerMsgRange.InnerExecNormalAttack)]
  public partial class InnerExecNormalAttack : MaoYouMessage
  {
    public FightInfo fightInfo;
  }

  [MemoryPackable]
  [Message(MaoInnerMsgRange.InnerUseSkillMsg)]
  public partial class InnerUseSkillMsg : MaoYouMessage
  {
    public FightInfo fightInfo;
    public SkillIdEnum skillId;
  }

  [MemoryPackable]
  [Message(MaoInnerMsgRange.InnerExecEscapeMsg)]
  public partial class InnerExecEscapeMsg : MaoYouMessage
  {
    public FightInfo fightInfo;
  }
}
