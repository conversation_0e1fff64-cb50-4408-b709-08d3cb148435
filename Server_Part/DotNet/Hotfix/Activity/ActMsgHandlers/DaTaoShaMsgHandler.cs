using System;
using System.Collections.Generic;
using System.Linq;

namespace MaoYouJi
{
  [MessageHandler(SceneType.Global)]
  public class ClientEntryDaToShaMsgHandler : MessageHandler<Entity, ClientEntryDaToShaMsg>
  {
    protected override async ETTask Run(Entity entity, ClientEntryDaToShaMsg msg)
    {
      using (await entity.Root().GetComponent<CoroutineLockComponent>().Wait(CoroutineLockType.DaTaoSha, msg.UserId))
      {
        LogicRet logicRet = UserProcSystem.GetUserWithCheck(msg.UserId, out User user);
        if (logicRet != LogicRet.Success)
        {
          user?.SendToast(logicRet.Message);
          return;
        }
        DaTaoShaActComp daTaoShaActComp = GlobalInfoCache.Instance.daTaoShaActComp;
        logicRet = daTaoShaActComp.CanEnterDaTaoSha(user);
        if (logicRet != LogicRet.Success)
        {
          user.SendToast(logicRet.Message);
          return;
        }
        // 随机移动到岛上的一个点
        MoveComponent moveComponent = user.GetComponent<MoveComponent>();
        List<string> dataShaPoints = GlobalInfoCache.Instance.mapPoints[MapNameConstant.DaTaoShaDao];
        string randomPoint = RandomGenerator.RandomArray(dataShaPoints);
        InnerMoveUserReq outMoveUserReq = new InnerMoveUserReq
        {
          TargetUserId = msg.UserId,
          OutNowMapName = moveComponent.nowMap,
          OutNowPointName = moveComponent.nowPoint,
          TargetMapName = MapNameConstant.DaTaoShaDao,
          TargetPointName = randomPoint,
          IsForceMove = false
        };
        InnerMoveUserResp outMoveUserResp = await entity.Root().GetComponent<MessageSender>().Call(user.GetParent<MapNode>().GetActorId(), outMoveUserReq) as InnerMoveUserResp;
        if (outMoveUserResp.Error != 0)
        {
          ETLog.Error($"进入大逃杀活动失败: {outMoveUserResp.Error} {outMoveUserResp.showMessage}");
          user.SendToast("进入大逃杀活动失败");
          return;
        }
        user.AddComponent<UserDaTaoShaInfoComp, DaTaoShaActComp>(daTaoShaActComp);
      }
    }
  }

  [MessageHandler(SceneType.Global)]
  public class ClientExitDaToShaMsgHandler : MessageHandler<Entity, ClientExitDaToShaMsg>
  {
    protected override async ETTask Run(Entity entity, ClientExitDaToShaMsg msg)
    {
      using (await entity.Root().GetComponent<CoroutineLockComponent>().Wait(CoroutineLockType.DaTaoSha, msg.UserId))
      {
        LogicRet logicRet = UserProcSystem.GetUserWithCheck(msg.UserId, out User user, true);
        if (logicRet != LogicRet.Success)
        {
          user?.SendToast(logicRet.Message);
          return;
        }
        DaTaoShaActComp daTaoShaActComp = GlobalInfoCache.Instance.daTaoShaActComp;
        logicRet = daTaoShaActComp.CanExitDaTaoSha(user);
        if (logicRet != LogicRet.Success)
        {
          user.SendToast(logicRet.Message);
          return;
        }

        string targetMapName = MapNameConstant.MaoYinCun;
        string targetPointName = "猫隐村广场";

        // 移动用户离开大逃杀岛
        MoveComponent moveComponent = user.GetComponent<MoveComponent>();
        InnerMoveUserReq outMoveUserReq = new InnerMoveUserReq
        {
          TargetUserId = msg.UserId,
          OutNowMapName = moveComponent.nowMap,
          OutNowPointName = moveComponent.nowPoint,
          TargetMapName = targetMapName,
          TargetPointName = targetPointName,
          IsForceMove = false // 强制移动
        };
        InnerMoveUserResp outMoveUserResp = await entity.Root().GetComponent<MessageSender>().Call(user.GetParent<MapNode>().GetActorId(), outMoveUserReq) as InnerMoveUserResp;
        if (outMoveUserResp.Error != 0)
        {
          ETLog.Error($"离开大逃杀活动失败: {outMoveUserResp.Error} {outMoveUserResp.showMessage}");
          user.SendToast("离开大逃杀活动失败");
          return;
        }

        // 恢复用户数据
        daTaoShaActComp.QuitDaTaoSha(user);

        int count = daTaoShaActComp.DaTaoShaUserList.Count;
        ETLog.Info($"离开大逃杀活动: {msg.UserId} {count}");
        ChatProSystem.SendMessageToAllUser(new ServerSendChatMsg
        {
          content = $"【大逃杀】 <u>{user.nickname}</u> 离开了大逃杀岛({count})！",
          chatType = ChatType.World_Chat
        });

        user.SendToast("成功离开大逃杀活动");
      }
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class ClientDaTaoShaCureMsgHandler : MessageLocationHandler<MapNode, ClientDaTaoShaCureMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientDaTaoShaCureMsg msg)
    {
      LogicRet getUserRet = nowMap.GetUserWithCheck(msg.UserId, out User user, true, true);
      if (!getUserRet.IsSuccess)
      {
        user?.SendToast(getUserRet.Message);
        return;
      }
      BagComponent bagSystem = user.GetComponent<BagComponent>();
      Thing daTaoShaKuangShi = bagSystem.GetThingInBag<Thing>(ThingNameEnum.DaTaoSha_KuangShi);
      if (daTaoShaKuangShi == null)
      {
        user.SendToast("背包中的矿石不足");
        return;
      }
      if (daTaoShaKuangShi.num < 3)
      {
        user.SendToast("背包中的矿石不足");
        return;
      }
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      attackComponent.blood = attackComponent.maxBlood;
      attackComponent.blue = attackComponent.maxBlue;
      bagSystem.AddThingNumWithSend(daTaoShaKuangShi, -3);
      user.SendMessage(ServerUpdatePartUserInfoMsg.Create(user, UserUpdateFlagEnum.Blood, UserUpdateFlagEnum.Blue));
      user.SendToast("治疗成功，消耗3个矿石！");
      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class ClientDaTaoShaAddAttrMsgHandler : MessageLocationHandler<MapNode, ClientDaTaoAddAttrMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientDaTaoAddAttrMsg msg)
    {
      LogicRet getUserRet = nowMap.GetUserWithCheck(msg.UserId, out User user, true, true);
      if (!getUserRet.IsSuccess)
      {
        user?.SendToast(getUserRet.Message);
        return;
      }

      BagComponent bagComponent = user.GetComponent<BagComponent>();

      Thing daTaoShaKuangShi = bagComponent.GetThingInBag<Thing>(ThingNameEnum.DaTaoSha_KuangShi);
      if (daTaoShaKuangShi == null)
      {
        user.SendToast("背包中的矿石不足");
        return;
      }
      if (daTaoShaKuangShi.num < 1)
      {
        user.SendToast("背包中的矿石不足");
        return;
      }

      bagComponent.AddThingNumWithSend(daTaoShaKuangShi, -1);

      UserDaTaoShaInfoComp daTaoShaInfoComp = user.GetComponent<UserDaTaoShaInfoComp>();
      if (daTaoShaInfoComp == null)
      {
        user.SendToast("您当前没有参与大逃杀活动哦！");
        return;
      }

      switch (msg.addType)
      {
        case 1:
          daTaoShaInfoComp.addPower += 1;
          break;
        case 2:
          daTaoShaInfoComp.addQuick += 1;
          break;
        case 3:
          daTaoShaInfoComp.addStrength += 1;
          break;
        case 4:
          daTaoShaInfoComp.addIq += 1;
          break;
        case 5:
          daTaoShaInfoComp.addMind += 1;
          break;
      }

      user.RecalcUserAttrs();

      user.SendMessage(ServerUpdatePartUserInfoMsg.Create(user, UserUpdateFlagEnum.Base_Attack));
      user.SendToast("属性增加成功！");
      await ETTask.CompletedTask;
    }

  }

  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class ClientDaTaoShaCompDrugMsgHandler : MessageLocationHandler<MapNode, ClientDaTaoCombDrugMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientDaTaoCombDrugMsg msg)
    {
      LogicRet getUserRet = nowMap.GetUserWithCheck(msg.UserId, out User user, true, true);
      if (!getUserRet.IsSuccess)
      {
        user?.SendToast(getUserRet.Message);
        return;
      }

      BagComponent bagComponent = user.GetComponent<BagComponent>();
      UserDaTaoShaInfoComp daTaoShaInfoComp = user.GetComponent<UserDaTaoShaInfoComp>();
      if (daTaoShaInfoComp == null)
      {
        user.SendToast("您当前没有参与大逃杀活动哦！");
        return;
      }

      ThingNameEnum thingNameEnum1 = ThingNameEnum.DaTaoSha_XiaoXueJi;
      ThingNameEnum thingNameEnum2 = ThingNameEnum.DaTaoSha_ZhongXueJi;

      switch (msg.compType)
      {
        case 2:
          thingNameEnum1 = ThingNameEnum.DaTaoSha_ZhongXueJi;
          thingNameEnum2 = ThingNameEnum.DaTaoSha_DaXueJi;
          break;
        case 3:
          thingNameEnum1 = ThingNameEnum.DaTaoSha_XiaoLanJi;
          thingNameEnum2 = ThingNameEnum.DaTaoSha_ZhongLanJi;
          break;
        case 4:
          thingNameEnum1 = ThingNameEnum.DaTaoSha_ZhongLanJi;
          thingNameEnum2 = ThingNameEnum.DaTaoSha_DaLanJi;
          break;
      }

      Thing thing1 = bagComponent.GetThingInBag<Thing>(thingNameEnum1);
      if (thing1 == null || thing1.num < 3)
      {
        user.SendToast($"背包中的材料不足");
        return;
      }

      bagComponent.AddThingNumWithSend(thing1, -3);

      ThingGiveInfo thingGiveInfo = new ThingGiveInfo(thingNameEnum2, OwnType.PRIVATE, 1);
      bagComponent.GiveThing(thingGiveInfo);

      user.SendToast("合成成功！");
      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class ClientDaTaoShaLeiDaMsgHandler : MessageLocationHandler<MapNode, ClientDaTaoShaLeiDaMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientDaTaoShaLeiDaMsg msg)
    {
      LogicRet getUserRet = nowMap.GetUserWithCheck(msg.UserId, out User user, true, true);
      if (!getUserRet.IsSuccess)
      {
        user?.SendToast(getUserRet.Message);
        return;
      }

      BagComponent bagComponent = user.GetComponent<BagComponent>();

      UserDaTaoShaInfoComp daTaoShaInfoComp = user.GetComponent<UserDaTaoShaInfoComp>();
      if (daTaoShaInfoComp == null)
      {
        user.SendToast("您当前没有参与大逃杀活动哦！");
        return;
      }

      Thing daTaoShaKuangShi = bagComponent.GetThingInBag<Thing>(ThingNameEnum.DaTaoSha_KuangShi);
      if (daTaoShaKuangShi == null)
      {
        user.SendToast("背包中的矿石不足");
        return;
      }
      if (daTaoShaKuangShi.num < 1)
      {
        user.SendToast("背包中的矿石不足");
        return;
      }
      DaTaoShaActComp daTaoShaActComp = GlobalInfoCache.Instance.daTaoShaActComp;
      var userIds = new HashSet<long>(daTaoShaActComp.DaTaoShaUserList);
      var showUserIds = new HashSet<long>();
      userIds.Remove(user.Id);

      if (userIds.Count > 5)
      {
        var limitedUsers = userIds.ToList().GetRange(0, Math.Min(5, userIds.Count));
        showUserIds.UnionWith(limitedUsers);
      }
      else
      {
        showUserIds.UnionWith(userIds);
      }

      if (showUserIds.Count == 0)
      {
        user.SendToast("当前没有其他玩家哦！");
        return;
      }

      string content = "【大逃杀】 雷达扫描结果，当前岛上的玩家位置是：";

      foreach (long userId in showUserIds)
      {
        LogicRet getOtherUserRet = UserProcSystem.GetUserWithCheck(userId, out User otherUser);
        if (getOtherUserRet.IsSuccess && otherUser != null)
        {
          MoveComponent moveComponent = otherUser.GetComponent<MoveComponent>();
          content += $" <u>{otherUser.nickname}</u>在<u>{moveComponent?.nowPoint}</u>,";
        }
      }

      user.SendMessage(new ServerSendChatMsg
      {
        content = content,
        chatType = ChatType.Sys_Chat
      });

      bagComponent.AddThingNumWithSend(daTaoShaKuangShi, -1);
      user.SendToast("玩家位置已经显示在系统聊天栏中");
      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class ClientDaTaoShaLearnSkillMsgHandler : MessageLocationHandler<MapNode, DaTaoShaLearnSkillReq, DaTaoShaLearnSkillResp>
  {
    protected override async ETTask Run(MapNode nowMap, DaTaoShaLearnSkillReq req, DaTaoShaLearnSkillResp resp)
    {
      LogicRet logicRet = nowMap.GetUserWithCheck(req.UserId, out User user, true, true);
      if (!logicRet.IsSuccess)
      {
        resp.SetError(logicRet.Message);
        return;
      }
      BagComponent bagSystem = user.GetComponent<BagComponent>();
      if (user.activityName != ActNameEnum.Da_TaoSha)
      {
        resp.SetError("您当前没有参与大逃杀活动哦！");
        return;
      }
      GlobalInfoCache.Instance.baseSkillCache.TryGetValue(req.skillId, out BaseSkill baseSkill);
      if (baseSkill == null)
      {
        resp.SetError("技能不存在");
        return;
      }
      if (!SkillConstant.IsDaTaoShaLearnSkills(req.skillId))
      {
        resp.SetError("无法在大逃杀活动中学习该技能！");
        return;
      }
      Thing daTaoShaKuangShi = bagSystem.GetThingInBag<Thing>(ThingNameEnum.DaTaoSha_KuangShi);
      if (daTaoShaKuangShi == null)
      {
        resp.SetError("背包中的矿石不足");
      }
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      SkillComponent skillComponent = attackComponent.GetComponent<SkillComponent>();
      Skill skill = skillComponent.GetSkill(req.skillId);
      if (req.level > 1 && skill == null)
      {
        resp.SetError("您没有学习过该技能！");
      }
      if (req.level > 1 && skill.level != req.level - 1)
      {
        resp.SetError("技能前置等级不够！");
      }
      if (baseSkill.skillInfos == null || baseSkill.skillInfos.Count < req.level)
      {
        resp.SetError("技能等级不存在！");
      }
      SkillInfo skillInfo = baseSkill.skillInfos[req.level - 1];
      if (skillInfo.jobLevel > attackComponent.level)
      {
        resp.SetError("角色等级不足！");
        return;
      }
      if (daTaoShaKuangShi.num < skillInfo.needGold)
      {
        resp.SetError("背包中的矿石不足");
        return;
      }
      bagSystem.AddThingNumWithSend(daTaoShaKuangShi, -(int)skillInfo.needGold);
      skill = GlobalInfoCache.Instance.GetSkill(req.skillId, req.level);
      skillComponent.AddLearnSkill(skill);
      user.SendMessage(ServerUpdateSkillInfoMsg.Create(skill));
      await ETTask.CompletedTask;
    }
  }
}