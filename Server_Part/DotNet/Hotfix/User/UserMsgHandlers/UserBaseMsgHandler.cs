namespace MaoYouJi
{
  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class ClientShowUserHandler : MessageLocationHandler<MapNode, ClientShowUserMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientShowUserMsg message)
    {
      User user = GlobalInfoCache.Instance.GetOnlineUser(message.targetUserId);
      if (user == null)
      {
        ETLog.Warning($"用户不存在: {message.targetUserId}");
        return;
      }
      User targetUser = user;
      if (message.targetUserId != user.Id)
      {
        targetUser = GlobalInfoCache.Instance.GetOnlineUser(message.targetUserId);
        if (targetUser == null)
        {
          user.SendToast("该用户不在线");
          return;
        }
      }
      user.SendMessage(new ServerShowUserMsg
      {
        userBaseDaoInfo = targetUser.GetUserBaseDaoInfo(),
        attackDaoInfo = targetUser.GetComponent<AttackComponent>().GetSimpleAttackDaoInfo()
      });
      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class ClientReviveUserHandler : MessageLocationHandler<MapNode, ClientReviveUserMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientReviveUserMsg message)
    {
      LogicRet logicRet = nowMap.GetUserWithCheck(message.UserId, out User user, true, false);
      if (!logicRet.IsSuccess)
      {
        user?.SendToast(logicRet.Message);
        return;
      }

      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      MoveComponent moveComponent = user.GetComponent<MoveComponent>();
      if (attackComponent.LiveState != LiveStateEnum.DEAD)
      {
        ETLog.Info($"复活用户失败: {user.Id}, {moveComponent.nowMap}, {moveComponent.nowPoint}, {attackComponent.LiveState}");
        user.SendToast("当前状态无法复活！");
        user.SendMessage(ServerUpdatePartUserInfoMsg.Create(user, UserUpdateFlagEnum.Blood, UserUpdateFlagEnum.Blue, UserUpdateFlagEnum.Live_State));
        return;
      }

      ETLog.Info($"复活用户: {user.Id}, {moveComponent.nowMap}, {moveComponent.nowPoint}, {attackComponent.LiveState}");

      // 获取复活地点
      var reviveMapInfo = MapConstant.getReviveMap(moveComponent.nowMap);
      MapNode mapNode = GlobalInfoCache.Instance.GetMapNode(reviveMapInfo.Key, reviveMapInfo.Value);
      if (mapNode == null)
      {
        mapNode = GlobalInfoCache.Instance.GetMapNode(MapNameConstant.MaoYinCun, "教堂");
      }

      // 复活用户
      user.ReviveUser(1, 1);

      // 移动用户到复活点
      moveComponent.MoveTo(mapNode);

      // 发送用户状态更新消息
      user.SendMessage(ServerUpdatePartUserInfoMsg.Create(user, UserUpdateFlagEnum.Blood, UserUpdateFlagEnum.Blue, UserUpdateFlagEnum.Live_State));

      // 向地图用户发送复活消息
      mapNode.SendMessageToMapUser(new ServerSendChatMsg
      {
        content = $"玩家 {user.nickname} 复活了！",
        chatType = ChatType.Local_Chat
      });

      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class ChangeSkinHandler : MessageLocationHandler<MapNode, ChangeSkinReq, ChangeSkinResp>
  {
    protected override async ETTask Run(MapNode nowMap, ChangeSkinReq request, ChangeSkinResp response)
    {
      LogicRet logicRet = nowMap.GetUserWithCheck(request.UserId, out User user, true, true);
      if (!logicRet.IsSuccess)
      {
        response.SetError(logicRet.Message);
        return;
      }
      if (user.activityName != ActNameEnum.None)
      {
        response.SetError("活动中不可更换皮肤");
        return;
      }
      if (!user.skinList.Contains(request.skinId))
      {
        response.SetError("您未拥有该皮肤");
        return;
      }
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      attackComponent.NowSkin = request.skinId;
      await ETTask.CompletedTask;
    }
  }
}