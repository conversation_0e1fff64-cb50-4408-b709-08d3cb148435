namespace MaoYouJi
{
  public static class LvGuanAfkSys
  {
    public static LogicRet EnterLvGuanFang(this User user, bool isGaoji)
    {
      LogicRet canEnterAfk = user.CanEnterAfk();
      if (!canEnterAfk.IsSuccess)
      {
        return canEnterAfk;
      }
      UserVipFuncInfo vipFuncInfo = user.GetComponent<UserVipFuncInfo>();
      if (isGaoji && vipFuncInfo.remainGaoJiLvGuanTime <= 0)
      {
        return LogicRet.Failed("高级旅馆时间不足");
      }
      if (!isGaoji && vipFuncInfo.remainLvGuanTime <= 0)
      {
        return LogicRet.Failed("旅馆时间不足");
      }
      long remainTime = 60;
      if (isGaoji && vipFuncInfo.remainGaoJiLvGuanTime < remainTime)
      {
        remainTime = vipFuncInfo.remainGaoJiLvGuanTime;
      }
      else if (!isGaoji && vipFuncInfo.remainLvGuanTime < remainTime)
      {
        remainTime = vipFuncInfo.remainLvGuanTime;
      }
      MapNode nowMapNode = user.GetParent<MapNode>();
      MapNode targetMapNode = isGaoji ? GlobalInfoCache.Instance.GetMapNode(MapNameConstant.GaoJiLvGuan, MapNameConstant.GaoJiLvGuan) : GlobalInfoCache.Instance.GetMapNode(MapNameConstant.LvGuan, MapNameConstant.LvGuan);
      LogicRet moveRet = user.GetComponent<MoveComponent>().MoveTo(targetMapNode);
      if (!moveRet.IsSuccess)
      {
        return moveRet;
      }
      VipAfkComp vipAfkComp = user.AddComponent<VipAfkComp, AfkSystemType>(AfkSystemType.LvGuan_Fang);
      vipAfkComp.setTime = remainTime;
      vipAfkComp.remainTime = remainTime;
      vipAfkComp.isGaoji = isGaoji;
      vipAfkComp.preMap = nowMapNode.mapName;
      vipAfkComp.prePoint = nowMapNode.pointName;
      vipAfkComp.enterTime = TimeInfo.Instance.ServerNow();

      // TODO: 需要实现旅馆相关的自动功能
      // startAutoSubVipTime(user);
      ETLog.Info($"enterLvGuan, user.id: {user.Id}, isGaoji: {isGaoji}, remainTime: {remainTime}, nowMap: {nowMapNode.mapName}, nowPoint: {nowMapNode.pointName}");
      return LogicRet.Success;
    }

    public static LogicRet QuitLvGuanFang(this User user)
    {
      VipAfkComp vipAfkComp = user.GetComponent<VipAfkComp>();
      if (vipAfkComp == null)
      {
        return LogicRet.Failed("挂机房间不存在");
      }
      if (vipAfkComp.afkSystemType != AfkSystemType.LvGuan_Fang)
      {
        return LogicRet.Failed("您不在旅馆中");
      }

      // TODO: 需要实现旅馆相关的停止功能
      // stopAutoSubVipTime(user);

      string preMap = "猫隐村", prePoint = "驿站";
      if (vipAfkComp.preMap != null && vipAfkComp.prePoint != null)
      {
        preMap = vipAfkComp.preMap;
        prePoint = vipAfkComp.prePoint;
      }
      LogicRet moveRet = user.GetComponent<MoveComponent>().MoveTo(preMap, prePoint);
      if (!moveRet.IsSuccess)
      {
        return moveRet;
      }
      user.RemoveComponent<VipAfkComp>();
      user.SendToast("旅馆挂机结束");
      return LogicRet.Success;
    }
  }
}