namespace MaoYouJi
{
  [MessageLocationHandler(SceneType.Map)]
  public class ClientEnterLianGongMsgHandler : MessageLocationHandler<MapNode, ClientEnterLianGongMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientEnterLianGongMsg message)
    {
      LogicRet canEnterAfk = nowMap.GetUserWithCheck(message.UserId, out User user, true, true);
      if (!canEnterAfk.IsSuccess)
      {
        user.SendToast(canEnterAfk.Message);
        return;
      }
      LogicRet enterLianGongRet = user.EnterLianGongFang(message.isGaoji);
      if (!enterLianGongRet.IsSuccess)
      {
        user.SendToast(enterLianGongRet.Message);
        return;
      }
      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  public class ClientQuitLianGongMsgHandler : MessageLocationHandler<MapNode, ClientQuitLianGongMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientQuitLianGongMsg message)
    {
      LogicRet canQuitAfk = nowMap.GetUserWithCheck(message.UserId, out User user, true, true);
      if (!canQuitAfk.IsSuccess)
      {
        user.SendToast(canQuitAfk.Message);
        return;
      }
      LogicRet quitLianGongRet = user.QuitLianGongFang();
      if (!quitLianGongRet.IsSuccess)
      {
        user.SendToast(quitLianGongRet.Message);
        return;
      }
      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  public class ClientChangeLianGongMsgHandler : MessageLocationHandler<MapNode, ClientChangeLianGongMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientChangeLianGongMsg message)
    {
      LogicRet canChangeAfk = nowMap.GetUserWithCheck(message.UserId, out User user, true, false);
      if (!canChangeAfk.IsSuccess)
      {
        user.SendToast(canChangeAfk.Message);
        return;
      }
      VipAfkComp vipAfkComp = user.GetComponent<VipAfkComp>();
      if (vipAfkComp == null || vipAfkComp.afkSystemType != AfkSystemType.LianGong_Fang)
      {
        user.SendToast("您不在练功房中！");
        return;
      }
      UserVipFuncInfo vipFuncInfo = user.GetComponent<UserVipFuncInfo>();
      if (message.setType == 0)
      {
        long remainTotal = vipAfkComp.isGaoji ? vipFuncInfo.remainGaoJiLianGongFangTime
            : vipFuncInfo.remainLianGongFangTime;
        if (remainTotal < message.setTime)
        {
          user.SendToast("设置的时间不能大于剩余总时间！");
          return;
        }
        if (message.setTime > 720)
        {
          user.SendToast("设置的时间不能大于720分钟！");
          return;
        }
        vipAfkComp.setTime = message.setTime;
        vipAfkComp.remainTime = message.setTime;
      }
      else
      {
        if (message.skillIdx < 0 || message.skillIdx >= 12)
        {
          user.SendToast("技能索引不合法");
          return;
        }
        SkillComponent skillComponent = user.GetComponent<AttackComponent>().GetComponent<SkillComponent>();
        SkillIdEnum skillId = skillComponent.equipSkills[message.skillIdx];
        if (skillId == SkillIdEnum.None)
        {
          user.SendToast("该快捷槽没有技能");
          return;
        }
        vipAfkComp.skillId = skillId;
      }
      user.SendToast("设置成功！");
      ServerNpcOptionMsg queryNpcTalkOptionsOut = new ServerNpcOptionMsg();
      queryNpcTalkOptionsOut.talkList = "show_liangongfang_time";
      queryNpcTalkOptionsOut.talkParamsMap = vipAfkComp.GetLianGongFangSetting();
      user.SendMessage(queryNpcTalkOptionsOut);
      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  public class ClientEnterLvGuanMsgHandler : MessageLocationHandler<MapNode, ClientEnterLvGuanMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientEnterLvGuanMsg message)
    {
      LogicRet canEnterAfk = nowMap.GetUserWithCheck(message.UserId, out User user, true, true);
      if (!canEnterAfk.IsSuccess)
      {
        user.SendToast(canEnterAfk.Message);
        return;
      }
      LogicRet enterLvGuanRet = user.EnterLvGuanFang(message.isGaoji);
      if (!enterLvGuanRet.IsSuccess)
      {
        user.SendToast(enterLvGuanRet.Message);
        return;
      }
      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  public class ClientQuitLvGuanMsgHandler : MessageLocationHandler<MapNode, ClientQuitLvGuanMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientQuitLvGuanMsg message)
    {
      LogicRet canQuitAfk = nowMap.GetUserWithCheck(message.UserId, out User user, true, true);
      if (!canQuitAfk.IsSuccess)
      {
        user.SendToast(canQuitAfk.Message);
        return;
      }

      LogicRet quitLvGuanRet = user.QuitLvGuanFang();
      if (!quitLvGuanRet.IsSuccess)
      {
        user.SendToast(quitLvGuanRet.Message);
        return;
      }

      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  public class ClientChangeLvGuanMsgHandler : MessageLocationHandler<MapNode, ClientChangeLvGuanMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientChangeLvGuanMsg message)
    {
      LogicRet canChangeAfk = nowMap.GetUserWithCheck(message.UserId, out User user, true, false);
      if (!canChangeAfk.IsSuccess)
      {
        user.SendToast(canChangeAfk.Message);
        return;
      }

      VipAfkComp vipAfkComp = user.GetComponent<VipAfkComp>();
      if (vipAfkComp == null || vipAfkComp.afkSystemType != AfkSystemType.LvGuan_Fang)
      {
        user.SendToast("您不在旅馆中！");
        return;
      }

      UserVipFuncInfo vipFuncInfo = user.GetComponent<UserVipFuncInfo>();
      long remainTotal = vipAfkComp.isGaoji ? vipFuncInfo.remainGaoJiLvGuanTime
          : vipFuncInfo.remainLvGuanTime;

      if (remainTotal < message.setTime)
      {
        user.SendToast("设置的时间不能大于剩余总时间！");
        return;
      }

      if (message.setTime > 720)
      {
        user.SendToast("设置的时间不能大于720分钟！");
        return;
      }

      vipAfkComp.setTime = message.setTime;
      vipAfkComp.remainTime = message.setTime;

      user.SendToast("设置成功！");

      ServerNpcOptionMsg queryNpcTalkOptionsOut = new ServerNpcOptionMsg();
      queryNpcTalkOptionsOut.talkList = "show_lvguan_time";
      queryNpcTalkOptionsOut.talkParamsMap = vipAfkComp.GetLvGuanSetting();
      user.SendMessage(queryNpcTalkOptionsOut);

      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  public class ClientEnterVfkMsgHandler : MessageLocationHandler<MapNode, ClientEnterVfkMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientEnterVfkMsg message)
    {
      LogicRet canEnterAfk = nowMap.GetUserWithCheck(message.UserId, out User user, true, true);
      if (!canEnterAfk.IsSuccess)
      {
        user.SendToast(canEnterAfk.Message);
        return;
      }
      LogicRet enterAfkRet = user.EnterCommonAfk(message.afkSystemType);
      if (!enterAfkRet.IsSuccess)
      {
        user.SendToast(enterAfkRet.Message);
        return;
      }
      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  public class ClientQuitVfkMsgHandler : MessageLocationHandler<MapNode, ClientQuitVfkMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientQuitVfkMsg message)
    {
      LogicRet canQuitAfk = nowMap.GetUserWithCheck(message.UserId, out User user, true, true);
      if (!canQuitAfk.IsSuccess)
      {
        user.SendToast(canQuitAfk.Message);
        return;
      }
      LogicRet quitAfkRet = user.QuitCommonAfk();
      if (!quitAfkRet.IsSuccess)
      {
        user.SendToast(quitAfkRet.Message);
        return;
      }
      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  public class ClientChangeVfkMsgHandler : MessageLocationHandler<MapNode, ClientChangeVfkMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientChangeVfkMsg message)
    {
      LogicRet canChangeAfk = nowMap.GetUserWithCheck(message.UserId, out User user, true, false);
      if (!canChangeAfk.IsSuccess)
      {
        user.SendToast(canChangeAfk.Message);
        return;
      }

      VipAfkComp vipAfkComp = user.GetComponent<VipAfkComp>();
      if (vipAfkComp == null)
      {
        user.SendToast("您不在挂机房间中！");
        return;
      }

      // 检查挂机类型是否为通用练功房类型
      if (vipAfkComp.afkSystemType != AfkSystemType.Chan_Fang &&
          vipAfkComp.afkSystemType != AfkSystemType.Dao_Fang &&
          vipAfkComp.afkSystemType != AfkSystemType.Gong_Chang)
      {
        user.SendToast("您不在通用挂机房间中！");
        return;
      }

      if (message.setTime > 240)
      {
        user.SendToast("设置的时间不能大于240分钟！");
        return;
      }

      vipAfkComp.setTime = message.setTime;
      vipAfkComp.remainTime = message.setTime;

      user.SendToast("设置成功！");

      ServerNpcOptionMsg queryNpcTalkOptionsOut = new ServerNpcOptionMsg();
      queryNpcTalkOptionsOut.talkList = "show_afk_room_time";
      queryNpcTalkOptionsOut.talkParamsMap = vipAfkComp.GetAfkSetting();
      user.SendMessage(queryNpcTalkOptionsOut);

      await ETTask.CompletedTask;
    }
  }
}