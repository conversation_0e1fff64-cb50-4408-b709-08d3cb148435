using System.Collections.Generic;

namespace MaoYouJi
{
  [EntitySystemOf(typeof(VipAfkComp))]
  [FriendOf(typeof(VipAfkComp))]
  public static partial class VipAfkCompSys
  {
    [EntitySystem]
    private static void Awake(this VipAfkComp self, AfkSystemType afkSystemType)
    {
      self.afkSystemType = afkSystemType;
    }

    public static LogicRet CanEnterAfk(this User user)
    {
      MapNode nowMapNode = user.GetParent<MapNode>();
      if (user.activityName != ActNameEnum.None)
      {
        return LogicRet.Failed("活动中无法进入挂机房间");
      }
      if (user.IsInVfxSystem())
      {
        return LogicRet.Failed("无法重复挂机");
      }
      if (nowMapNode.nodeType != MapNodeType.CITY)
      {
        return LogicRet.Failed("只能在城镇地区进入挂机房间");
      }
      if (user.HasUserState(UserStateEnum.Evil_State))
      {
        return LogicRet.Failed("红名状态无法挂机");
      }
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      if (attackComponent.LiveState != LiveStateEnum.ALIVE)
      {
        return LogicRet.Failed("当前状态无法挂机");
      }
      return LogicRet.Success;
    }

    public static Dictionary<string, string> GetLianGongFangSetting(this VipAfkComp self)
    {
      User user = self.GetParent<User>();
      UserVipFuncInfo vipFuncInfo = user.GetComponent<UserVipFuncInfo>();
      SkillComponent skillComponent = user.GetComponent<AttackComponent>().GetComponent<SkillComponent>();
      string skillId = "无";
      if (self.skillId != SkillIdEnum.None)
      {
        Skill skill = skillComponent.GetSkill(self.skillId);
        if (skill != null)
        {
          skillId = skill.name;
        }
      }
      Dictionary<string, string> settingMap = new Dictionary<string, string>();
      settingMap["remainTime"] = self.remainTime.ToString();
      settingMap["setTime"] = self.setTime.ToString();
      settingMap["enterTime"] = self.enterTime.ToString();
      settingMap["totalExp"] = self.totalExp.ToString();
      settingMap["skillId"] = skillId;
      long remainTotal = self.isGaoji ? vipFuncInfo.remainGaoJiLianGongFangTime
          : vipFuncInfo.remainLianGongFangTime;
      settingMap["remainTotal"] = remainTotal.ToString();
      return settingMap;
    }

    public static Dictionary<string, string> GetLvGuanSetting(this VipAfkComp self)
    {
      User user = self.GetParent<User>();
      UserVipFuncInfo vipFuncInfo = user.GetComponent<UserVipFuncInfo>();
      Dictionary<string, string> settingMap = new Dictionary<string, string>();
      settingMap["remainTime"] = self.remainTime.ToString();
      settingMap["setTime"] = self.setTime.ToString();
      settingMap["enterTime"] = self.enterTime.ToString();
      settingMap["totalExp"] = self.totalExp.ToString();
      settingMap["existTime"] = self.existTime.ToString();
      settingMap["addZenExp"] = self.addZenExp.ToString();
      long remainTotal = self.isGaoji ? vipFuncInfo.remainGaoJiLvGuanTime
          : vipFuncInfo.remainLvGuanTime;
      settingMap["remainTotal"] = remainTotal.ToString();
      return settingMap;
    }

    public static Dictionary<string, string> GetAfkSetting(this VipAfkComp self)
    {
      Dictionary<string, string> settingMap = new();
      settingMap["remainTime"] = self.remainTime.ToString();
      settingMap["setTime"] = self.setTime.ToString();
      settingMap["enterTime"] = self.enterTime.ToString();
      settingMap["totalExp"] = self.totalExp.ToString();
      return settingMap;
    }
  }
}