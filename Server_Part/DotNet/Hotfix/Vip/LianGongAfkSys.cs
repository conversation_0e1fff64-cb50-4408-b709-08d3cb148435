namespace MaoYouJi
{
  public static class LianGongAfkSys
  {
    public static LogicRet EnterLianGongFang(this User user, bool isGaoji)
    {
      LogicRet canEnterAfk = user.CanEnterAfk();
      if (!canEnterAfk.IsSuccess)
      {
        return canEnterAfk;
      }
      UserVipFuncInfo vipFuncInfo = user.GetComponent<UserVipFuncInfo>();
      if (isGaoji && vipFuncInfo.remainGaoJiLianGongFangTime <= 0)
      {
        return LogicRet.Failed("高级练功房时间不足");
      }
      if (!isGaoji && vipFuncInfo.remainLianGongFangTime <= 0)
      {
        return LogicRet.Failed("练功房时间不足");
      }
      long remainTime = 60;
      if (isGaoji && vipFuncInfo.remainGaoJiLianGongFangTime < remainTime)
      {
        remainTime = vipFuncInfo.remainGaoJiLianGongFangTime;
      }
      else if (!isGaoji && vipFuncInfo.remainLianGongFangTime < remainTime)
      {
        remainTime = vipFuncInfo.remainLianGongFangTime;
      }
      MapNode nowMapNode = user.GetParent<MapNode>();
      MapNode targetMapNode = isGaoji ? GlobalInfoCache.Instance.GetMapNode(MapNameConstant.GaoJiLianGongFang, MapNameConstant.GaoJiLianGongFang) : GlobalInfoCache.Instance.GetMapNode(MapNameConstant.LianGongFang, MapNameConstant.LianGongFang);
      LogicRet moveRet = user.GetComponent<MoveComponent>().MoveTo(targetMapNode);
      if (!moveRet.IsSuccess)
      {
        return moveRet;
      }
      VipAfkComp vipAfkComp = user.AddComponent<VipAfkComp, AfkSystemType>(AfkSystemType.LianGong_Fang);
      vipAfkComp.setTime = remainTime;
      vipAfkComp.remainTime = remainTime;
      vipAfkComp.preMap = nowMapNode.mapName;
      vipAfkComp.prePoint = nowMapNode.pointName;
      vipAfkComp.enterTime = TimeInfo.Instance.ServerNow();

      // startAutoGenLiangGong(user);
      // startAutoSubVipTime(user);
      ETLog.Info($"enterLianGong, user.id: {user.Id}, isGaoji: {isGaoji}, remainTime: {remainTime}, nowMap: {nowMapNode.mapName}, nowPoint: {nowMapNode.pointName}");
      return LogicRet.Success;
    }

    public static LogicRet QuitLianGongFang(this User user)
    {
      VipAfkComp vipAfkComp = user.GetComponent<VipAfkComp>();
      if (vipAfkComp == null)
      {
        return LogicRet.Failed("挂机房间不存在");
      }
      // stopAutoGenLiangGong(user);
      // stopAutoSubVipTime(user);

      string preMap = "猫隐村", prePoint = "驿站";
      if (vipAfkComp.preMap != null && vipAfkComp.prePoint != null)
      {
        preMap = vipAfkComp.preMap;
        prePoint = vipAfkComp.prePoint;
      }
      LogicRet moveRet = user.GetComponent<MoveComponent>().MoveTo(preMap, prePoint);
      if (!moveRet.IsSuccess)
      {
        return moveRet;
      }
      user.RemoveComponent<VipAfkComp>();
      user.SendToast("练功房挂机结束");
      return LogicRet.Success;
    }
  }
}