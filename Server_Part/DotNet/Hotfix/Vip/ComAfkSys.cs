namespace MaoYouJi
{
  public static class ComAfkSys
  {
    public static LogicRet EnterCommonAfk(this User user, AfkSystemType afkSystemType)
    {
      LogicRet canEnterAfk = user.CanEnterAfk();
      if (!canEnterAfk.IsSuccess)
      {
        return canEnterAfk;
      }
      long remainTime = 60;
      string mapName = "工厂";
      if (afkSystemType == AfkSystemType.Chan_Fang)
      {
        mapName = MapNameConstant.ChanFang;
      }
      else if (afkSystemType == AfkSystemType.Dao_Fang)
      {
        mapName = MapNameConstant.DaoGuan;
      }
      else if (afkSystemType == AfkSystemType.Gong_Chang)
      {
        mapName = MapNameConstant.GongChang;
      }
      MapNode nowMap = user.GetParent<MapNode>();
      MapNode targetMap = GlobalInfoCache.Instance.GetMapNode(mapName, mapName);
      LogicRet moveRet = user.GetComponent<MoveComponent>().MoveTo(targetMap);
      if (!moveRet.IsSuccess)
      {
        return moveRet;
      }
      VipAfkComp vipAfkComp = user.AddComponent<VipAfkComp, AfkSystemType>(afkSystemType);
      vipAfkComp.isCalced = false;
      vipAfkComp.setTime = remainTime;
      vipAfkComp.remainTime = remainTime;
      vipAfkComp.preMap = nowMap.mapName;
      vipAfkComp.prePoint = nowMap.pointName;
      vipAfkComp.enterTime = TimeInfo.Instance.ServerNow();
      // startAutoSubVipTime(user);
      ETLog.Info($"enterAfk_{afkSystemType.ToString()}_{user.Id}_{remainTime}_{nowMap.mapName}_{nowMap.pointName}");
      return LogicRet.Success;
    }

    public static LogicRet QuitCommonAfk(this User user)
    {
      VipAfkComp vipAfkComp = user.GetComponent<VipAfkComp>();
      if (vipAfkComp == null)
      {
        return LogicRet.Failed("挂机房间不存在");
      }
      if (vipAfkComp.afkSystemType != AfkSystemType.Chan_Fang &&
          vipAfkComp.afkSystemType != AfkSystemType.Dao_Fang &&
          vipAfkComp.afkSystemType != AfkSystemType.Gong_Chang)
      {
        return LogicRet.Failed("您不在通用挂机房间中！");
      }
      ETLog.Info($"quitAfk_{user.Id}_{vipAfkComp.afkSystemType.ToString()}");
      // stopAutoSubVipTime(user);
      string preMap = "猫隐村", prePoint = "驿站";
      if (vipAfkComp.preMap != null && vipAfkComp.prePoint != null)
      {
        preMap = vipAfkComp.preMap;
        prePoint = vipAfkComp.prePoint;
      }
      LogicRet moveRet = user.GetComponent<MoveComponent>().MoveTo(preMap, prePoint);
      if (!moveRet.IsSuccess)
      {
        return moveRet;
      }
      string afkName = EnumDescriptionCache.GetDescription(vipAfkComp.afkSystemType);
      user.SendToast($"{afkName}挂机结束");
      user.SendChat($"{afkName}挂机结束，您获得{vipAfkComp.totalExp}点{afkName}经验");
      user.RemoveComponent<VipAfkComp>();
      return LogicRet.Success;
    }
  }
}