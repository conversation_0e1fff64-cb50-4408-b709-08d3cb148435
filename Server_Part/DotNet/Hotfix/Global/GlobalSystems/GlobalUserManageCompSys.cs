using System;
using System.Collections.Generic;
using System.Linq;
using MongoDB.Driver;
using MongoDB.Bson;

namespace MaoYouJi
{
  [FriendOf(typeof(GlobalManageComp))]
  public static partial class GlobalUserManageCompSys
  {
    public static async ETTask RankUserByAttackNum(this GlobalManageComp self)
    {
      try
      {
        // 从数据库获取排名前100的用户，使用MongoDB聚合管道在数据库层面完成排序
        DBManagerComponent dbManagerComponent = self.GetParent<Scene>().GetComponent<DBManagerComponent>();
        DBComponent dbComponent = dbManagerComponent.GetMyZoneDB();

        // 构建MongoDB聚合管道，参照FiberUsersComponentSystem中的实现
        var pipeline = new[]
        {
          // 第一步：添加attackComponent字段，从C数组中提取AttackComponent组件
          new BsonDocument("$addFields", new BsonDocument
          {
            { "attackComponent", new BsonDocument("$arrayElemAt", new BsonArray
              {
                new BsonDocument("$filter", new BsonDocument
                {
                  { "input", "$C" },
                  { "as", "comp" },
                  { "cond", new BsonDocument("$eq", new BsonArray { "$$comp._t", "AttackComponent" }) }
                }),
                0
              })
            }
          }),
          // 第二步：过滤掉没有AttackComponent的用户
          new BsonDocument("$match", new BsonDocument
          {
            { "attackComponent", new BsonDocument("$ne", BsonNull.Value) }
          }),
          // 第三步：按攻击力降序排序
          new BsonDocument("$sort", new BsonDocument
          {
            { "attackComponent.attackNum", -1 }
          }),
          // 第四步：限制前100名
          new BsonDocument("$limit", 100),
          // 第五步：投影只返回需要的字段
          new BsonDocument("$project", new BsonDocument
          {
            { "_id", 0 },
            { "userId", "$Id" },
            { "name", "$nickname" },
            { "attackVal", "$attackComponent.attackNum" },
            { "skinId", "$attackComponent.NowSkin" }
          })
        };

        // 执行聚合查询
        var collection = dbComponent.GetCollection<BsonDocument>("MaoYouJi.User");
        var cursor = await collection.AggregateAsync<RankItem>(pipeline);
        List<RankItem> dbRankItems = await cursor.ToListAsync();

        ETLog.Info($"从数据库获取到{dbRankItems.Count}名用户的排行榜数据");

        // 获取在线用户，处理实时战斗力更新
        var onlineUsers = GlobalInfoCache.Instance.allOnlineUserCache.Values;

        // 使用Dictionary来处理重复用户，key为userId，保证同一用户只保留最新的战斗力数据
        Dictionary<long, RankItem> rankMap = new Dictionary<long, RankItem>();

        // 先添加数据库中的用户数据
        long minAttackVal = 0;
        foreach (RankItem dbItem in dbRankItems)
        {
          rankMap[dbItem.userId] = dbItem;
          if (minAttackVal == 0 || minAttackVal > dbItem.attackVal)
          {
            minAttackVal = dbItem.attackVal;
          }
        }

        // 处理在线用户，如果在线用户的战斗力更高，则更新排名信息
        foreach (User onlineUser in onlineUsers)
        {
          AttackComponent attackComponent = onlineUser.GetComponent<AttackComponent>();
          if (attackComponent == null) continue;

          long realAttackVal = attackComponent.attackNum;

          // 大逃杀活动的特殊处理逻辑
          if (onlineUser.activityName == ActNameEnum.Da_TaoSha)
          {
            UserDaTaoShaInfoComp daTaoShaUserInfo = onlineUser.GetComponent<UserDaTaoShaInfoComp>();
            AttackDaoInfo attackDaoInfo = daTaoShaUserInfo?.attackDaoInfo;
            if (attackDaoInfo != null)
            {
              realAttackVal = attackDaoInfo.attackNum;
            }
          }

          if (rankMap.TryGetValue(onlineUser.Id, out RankItem existingItem))
          {
            // 如果在线用户的战斗力更高，则更新
            if (existingItem.attackVal < realAttackVal)
            {
              existingItem.attackVal = realAttackVal;
              existingItem.skinId = attackComponent.NowSkin;
            }
          }
          else if (realAttackVal > minAttackVal)
          {
            // 如果在线用户不在排行榜中，但战斗力足够高，则添加
            RankItem rankItem = new RankItem
            {
              userId = onlineUser.Id,
              name = onlineUser.nickname,
              attackVal = realAttackVal,
              skinId = attackComponent.NowSkin
            };
            rankMap[onlineUser.Id] = rankItem;
          }
        }

        // 将Dictionary转换为List并按战斗力排序
        List<RankItem> rankItems = rankMap.Values.ToList();
        rankItems.Sort((a, b) => b.attackVal.CompareTo(a.attackVal));

        // 只保留前100名
        if (rankItems.Count > 100)
        {
          rankItems = rankItems.Take(100).ToList();
        }

        // 移除战斗力为0的用户
        rankItems.RemoveAll(item => item.attackVal == 0);

        ETLog.Info($"最终排行榜用户数量: {rankItems.Count}");

        // 保存排行榜信息
        RankInfo rankInfo = new RankInfo
        {
          id = 8888,
          items = rankItems,
          updateTime = TimeInfo.Instance.ServerNow()
        };

        // 更新GlobalManageComp中的排行榜信息
        self.rankInfo = rankInfo;

        // 保存到数据库
        var filter = Builders<RankInfo>.Filter.Eq(r => r.id, rankInfo.id);
        await dbComponent.SaveClass(filter, rankInfo);

        ETLog.Info($"排行榜更新完成，共{rankItems.Count}名用户");
      }
      catch (Exception e)
      {
        ETLog.Error($"更新用户战斗力排行榜失败: {e}");
      }
    }
  }
}