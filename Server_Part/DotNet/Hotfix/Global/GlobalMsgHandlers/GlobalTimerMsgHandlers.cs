namespace MaoYouJi
{
  [MessageHandler(SceneType.Global)]
  public class InnerGlobalTimerPer_1_MinMsgHandler : MessageHandler<Scene, InnerGlobalTimerPer_1_MinMsg>
  {
    protected override async ETTask Run(Scene scene, InnerGlobalTimerPer_1_MinMsg message)
    {
      ETLog.Info($"InnerGlobalTimerPer_1_MinMsgHandler");
      await ETTask.CompletedTask;
    }
  }

  [MessageHandler(SceneType.Global)]
  public class InnerGlobalTimerPer_5_MinMsgHandler : MessageHandler<Scene, InnerGlobalTimerPer_5_MinMsg>
  {
    protected override async ETTask Run(Scene scene, InnerGlobalTimerPer_5_MinMsg message)
    {
      ETLog.Info($"InnerGlobalTimerPer_5_MinMsgHandler");
      await ETTask.CompletedTask;
    }
  }

  [MessageHandler(SceneType.Global)]
  public class InnerGlobalTimerPer_10_MinMsgHandler : MessageHandler<Scene, InnerGlobalTimerPer_10_MinMsg>
  {
    protected override async ETTask Run(Scene scene, InnerGlobalTimerPer_10_MinMsg message)
    {
      ETLog.Info($"InnerGlobalTimerPer_10_MinMsgHandler");
      await ETTask.CompletedTask;
    }
  }

  [MessageHandler(SceneType.Global)]
  public class InnerGlobalTimerPer_30_MinMsgHandler : MessageHandler<Scene, InnerGlobalTimerPer_30_MinMsg>
  {
    protected override async ETTask Run(Scene scene, InnerGlobalTimerPer_30_MinMsg message)
    {
      ETLog.Info($"InnerGlobalTimerPer_30_MinMsgHandler");
      GlobalManageComp globalManageComp = scene.GetComponent<GlobalManageComp>();
      await globalManageComp.RankUserByAttackNum();
      await ETTask.CompletedTask;
    }
  }

  [MessageHandler(SceneType.Global)]
  public class InnerGlobalTimerPer_1_HourMsgHandler : MessageHandler<Scene, InnerGlobalTimerPer_1_HourMsg>
  {
    protected override async ETTask Run(Scene scene, InnerGlobalTimerPer_1_HourMsg message)
    {
      ETLog.Info($"InnerGlobalTimerPer_1_HourMsgHandler");
      await ETTask.CompletedTask;
    }
  }

  [MessageHandler(SceneType.Global)]
  public class InnerGlobalTimerPer_1_DayMsgHandler : MessageHandler<Scene, InnerGlobalTimerPer_1_DayMsg>
  {
    protected override async ETTask Run(Scene scene, InnerGlobalTimerPer_1_DayMsg message)
    {
      ETLog.Info($"InnerGlobalTimerPer_1_DayMsgHandler");
      await ETTask.CompletedTask;
    }
  }
}