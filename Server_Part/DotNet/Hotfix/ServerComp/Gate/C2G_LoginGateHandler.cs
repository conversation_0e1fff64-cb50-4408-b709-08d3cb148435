using System;
using System.Collections.Generic;
using MongoDB.Bson;
using MongoDB.Driver;


namespace MaoYouJi
{
  [MessageSessionHandler(SceneType.Gate)]
  [FriendOf(typeof(Account))]
  public class C2G_LoginGateHandler : MessageSessionHandler<GateLogInReq, GateLogInResp>
  {
    protected override async ETTask Run(Session session, GateLogInReq request, GateLogInResp response)
    {
      Scene root = session.Root();
      long netAccountId = root.GetComponent<GateSessionKeyComponent>().Get(request.Key);
      if (netAccountId == 0)
      {
        response.Error = ErrorCore.ERR_Show_Msg;
        response.showMessage = "网关验证密钥失败!";
        return;
      }
      // 根据网关账户ID获取本地账号
      DBComponent dBComponent = root.GetComponent<DBManagerComponent>().GetMyZoneDB();
      GateAccountsComponent gateAccountsComponent = root.GetComponent<GateAccountsComponent>();
      Account account = await gateAccountsComponent.GetAccount(netAccountId);
      long accessKey = RandomGenerator.RandInt64();
      if (account == null)
      {
        account = gateAccountsComponent.AddChild<Account>();
        account.accessKey = accessKey;
        account.netAccountId = netAccountId;
        account.keyExpireTime = TimeInfo.Instance.ServerNow() + 3600;
        account.userType = UserType.NORMAL;
        account.roleList = new List<long>();
        account.createTime = TimeInfo.Instance.ServerNow();
        account.lastLogInTime = TimeInfo.Instance.ServerNow();
        account.AddComponent<BagComponent, BagType, int>(BagType.PublicStore, 20);
        account.BeginInit();
        await dBComponent.Insert(account.Id, account);
      }
      account.accessKey = accessKey;
      GlobalInfoCache.Instance.AddOnlineAccount(account.netAccountId, account);
      List<AccountRoleInfo> accountRoleInfoList = await account.GetRoleInfoListEmbedded();
      response.accountRoles = accountRoleInfoList;
      response.accessKey = accessKey;

      session.RemoveComponent<SessionAcceptTimeoutComponent>();

      // PlayerComponent playerComponent = root.GetComponent<PlayerComponent>();
      // Player player = playerComponent.GetByAccount(account);
      // if (player == null)
      // {
      //   player = playerComponent.AddChild<Player, string>(account);
      //   playerComponent.Add(player);
      //   PlayerSessionComponent playerSessionComponent = player.AddComponent<PlayerSessionComponent>();
      //   playerSessionComponent.AddComponent<MailBoxComponent, MailBoxType>(MailBoxType.GateSession);
      //   await playerSessionComponent.AddLocation(LocationType.GateSession);

      //   player.AddComponent<MailBoxComponent, MailBoxType>(MailBoxType.UnOrderedMessage);
      //   await player.AddLocation(LocationType.Player);

      //   session.AddComponent<SessionPlayerComponent>().Player = player;
      //   playerSessionComponent.Session = session;
      // }

      // response.PlayerId = player.Id;
    }
  }
}