using MongoDB.Driver;


namespace MaoYouJi
{
  [MessageSessionHandler(SceneType.Gate)]
  public class C2G_CheckNickNameHandler : MessageSessionHandler<CheckNicknameReq, CheckNicknameResp>
  {
    protected override async ETTask Run(Session session, CheckNicknameReq request, CheckNicknameResp response)
    {
      FiberUsersComponent gateUsersComponent = session.Root().GetComponent<FiberUsersComponent>();
      if (!gateUsersComponent.CheckNickName(request.nickname, out string msg))
      {
        response.Error = ErrorCore.ERR_Show_Msg;
        response.showMessage = msg;
        return;
      }
      await ETTask.CompletedTask;
    }
  }
}