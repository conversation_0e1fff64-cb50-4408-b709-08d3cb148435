using System.Collections.Generic;
using System.Text;

namespace MaoYouJi
{
  [Invoke((long)ThingNameEnum.Yin<PERSON>hen_Yao<PERSON>hui)]
  public class UseYinShenYaoShuiInvoker : AInvokeHandler<UseThingStruct, ETTask<LogicRet>>
  {
    public override async ETTask<LogicRet> Handle(UseThingStruct args)
    {
      MapNode mapNode = args.map;
      User user = args.user;
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      if (user.userStates.ContainsKey(UserStateEnum.YingShen_State))
      {
        return LogicRet.Failed("您已经处于隐身状态！");
      }
      Treasure treasure = (Treasure)args.thing;
      UserStateDetail userStateDetail = new UserStateDetail();
      userStateDetail.userState = UserStateEnum.YingShen_State;
      userStateDetail.val = [60 * 1000 * 10];
      userStateDetail.existTime = 60 * 1000 * 10;
      userStateDetail.endTime = TimeInfo.Instance.ServerNow() + userStateDetail.existTime;
      user.AddUserStatus(userStateDetail);
      bagComponent.AddThingNumWithSend(treasure, -1);
      mapNode.PutUserInMapNode(user);
      mapNode.SendMessageToMapUser(new ServerSendChatMsg
      {
        content = $"{user.nickname}使用了隐身药水，瞬间消失不见了。",
        chatType = ChatType.Local_Chat
      });
      user.SendToast("使用隐身药水成功！");
      await ETTask.CompletedTask;
      return LogicRet.Success;
    }
  }

  [Invoke((long)ThingNameEnum.GPS_DingWei)]
  public class UseGPSDingWeiInvoker : AInvokeHandler<UseThingStruct, ETTask<LogicRet>>
  {
    public override async ETTask<LogicRet> Handle(UseThingStruct args)
    {
      BagComponent bagComponent = args.user.GetComponent<BagComponent>();
      ClientUseThingMsg msg = args.msg;
      if (msg.targetUserId == null)
      {
        return LogicRet.Failed("请输入目标用户id或名称");
      }
      User user = args.user;
      Treasure treasure = (Treasure)args.thing;
      FiberUsersComponent fiberUsersComponent = args.map.Root().GetComponent<FiberUsersComponent>();
      User targetUser = await fiberUsersComponent.GetOnlineUserBuyIdOrName(msg.targetUserId);
      if (targetUser == null)
      {
        return LogicRet.Failed("目标用户不存在或不在线");
      }
      if (targetUser.offlineState == OfflineStateEnum.OFFLINE)
      {
        return LogicRet.Failed("目标用户不在线");
      }
      targetUser.SendChat("一束神秘的光线扫过，您的位置暴露了！");
      MoveComponent moveComponent = targetUser.GetComponent<MoveComponent>();
      bagComponent.AddThingNumWithSend(treasure, -1);
      user.SendToast($"{targetUser.nickname}在：{moveComponent.nowMap}/{moveComponent.nowPoint}");
      user.SendChat($"{targetUser.nickname}在：{moveComponent.nowMap}/{moveComponent.nowPoint}");
      await ETTask.CompletedTask;
      return LogicRet.Success;
    }
  }

  [Invoke((long)ThingNameEnum.JinChan_TuoQiao)]
  public class UseJinChanTuoQiaoInvoker : AInvokeHandler<UseThingStruct, ETTask<LogicRet>>
  {
    public override async ETTask<LogicRet> Handle(UseThingStruct args)
    {
      User user = args.user;
      MapNode mapNode = args.map;
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      InFightComponent inFightComponent = attackComponent.GetComponent<InFightComponent>();
      if (inFightComponent == null)
      {
        return LogicRet.Failed("您没有正在进行的战斗！");
      }
      LogicRet rlt = await inFightComponent.OutCallJinChanTuoQiao(args.thing);
      if (!rlt.IsSuccess)
      {
        return LogicRet.Failed(rlt.Message);
      }
      if (attackComponent.LiveState == LiveStateEnum.ALIVE)
      {
        mapNode.SendMessageToMapUser(new ServerSendChatMsg
        {
          content = $"{user.nickname}使用金蝉脱壳从战斗中逃脱了。",
          chatType = ChatType.Local_Chat
        });
      }
      user.SendToast("使用金蝉脱壳成功！");
      return LogicRet.Success;
    }
  }

  [Invoke((long)ThingNameEnum.Da_LaBa)]
  public class UseDaLaBaInvoker : AInvokeHandler<UseThingStruct, ETTask<LogicRet>>
  {
    public override async ETTask<LogicRet> Handle(UseThingStruct args)
    {
      User user = args.user;
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      ClientUseThingMsg msg = args.msg;
      if (msg.msg == null)
      {
        return LogicRet.Failed("请输入消息");
      }
      if (msg.msg.Length > 20)
      {
        return LogicRet.Failed("消息长度不能超过20个字符");
      }
      ChatProSystem.SendGlobalNotice(msg.msg, ChatType.User_Chat, user.nickname);
      bagComponent.AddThingNumWithSend(args.thing, -1);
      user.SendToast("使用大喇叭成功！");
      await ETTask.CompletedTask;
      return LogicRet.Success;
    }
  }

  [Invoke((long)ThingNameEnum.LianGongFang_MenKa)]
  public class UseLianGongFangMenKaInvoker : AInvokeHandler<UseThingStruct, ETTask<LogicRet>>
  {
    public override async ETTask<LogicRet> Handle(UseThingStruct args)
    {
      Treasure treasure = (Treasure)args.thing;
      User user = args.user;
      ClientUseThingMsg msg = args.msg;
      if (treasure == null || treasure.num < msg.thingNum)
      {
        return LogicRet.Failed("您没有足够的练功房门卡！");
      }
      UserVipFuncInfo vipFuncInfo = user.GetComponent<UserVipFuncInfo>();
      vipFuncInfo.remainLianGongFangTime += msg.thingNum * 60;
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      bagComponent.AddThingNumWithSend(treasure, -msg.thingNum);
      user.SendToast("使用练功房门卡成功！");
      await ETTask.CompletedTask;
      return LogicRet.Success;
    }
  }

  [Invoke((long)ThingNameEnum.GaoJi_LianGongFang_MenKa)]
  public class UseGaoJiLianGongFangMenKaInvoker : AInvokeHandler<UseThingStruct, ETTask<LogicRet>>
  {
    public override async ETTask<LogicRet> Handle(UseThingStruct args)
    {
      Treasure treasure = (Treasure)args.thing;
      User user = args.user;
      ClientUseThingMsg msg = args.msg;
      if (treasure == null || treasure.num < msg.thingNum)
      {
        return LogicRet.Failed("您没有足够的练功房门卡！");
      }
      UserVipFuncInfo vipFuncInfo = user.GetComponent<UserVipFuncInfo>();
      vipFuncInfo.remainGaoJiLianGongFangTime += msg.thingNum * 60;
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      bagComponent.AddThingNumWithSend(treasure, -msg.thingNum);
      user.SendToast("使用高级练功房门卡成功！");
      await ETTask.CompletedTask;
      return LogicRet.Success;
    }
  }

  [Invoke((long)ThingNameEnum.LvGuan_MenKa)]
  public class UseLvGuanMenKaInvoker : AInvokeHandler<UseThingStruct, ETTask<LogicRet>>
  {
    public override async ETTask<LogicRet> Handle(UseThingStruct args)
    {
      Treasure treasure = (Treasure)args.thing;
      User user = args.user;
      ClientUseThingMsg msg = args.msg;
      if (treasure == null || treasure.num < msg.thingNum)
      {
        return LogicRet.Failed("您没有足够的旅馆门卡！");
      }
      UserVipFuncInfo vipFuncInfo = user.GetComponent<UserVipFuncInfo>();
      vipFuncInfo.remainLvGuanTime += msg.thingNum * 60;
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      bagComponent.AddThingNumWithSend(treasure, -msg.thingNum);
      user.SendToast("使用旅馆门卡成功！");
      await ETTask.CompletedTask;
      return LogicRet.Success;
    }
  }

  [Invoke((long)ThingNameEnum.GaoJi_LvGuan_MenKa)]
  public class UseGaoJiLvGuanMenKaInvoker : AInvokeHandler<UseThingStruct, ETTask<LogicRet>>
  {
    public override async ETTask<LogicRet> Handle(UseThingStruct args)
    {
      Treasure treasure = (Treasure)args.thing;
      User user = args.user;
      ClientUseThingMsg msg = args.msg;
      if (treasure == null || treasure.num < msg.thingNum)
      {
        return LogicRet.Failed("您没有足够的高级旅馆门卡！");
      }
      UserVipFuncInfo vipFuncInfo = user.GetComponent<UserVipFuncInfo>();
      vipFuncInfo.remainGaoJiLvGuanTime += msg.thingNum * 60;
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      bagComponent.AddThingNumWithSend(treasure, -msg.thingNum);
      user.SendToast("使用高级旅馆门卡成功！");
      await ETTask.CompletedTask;
      return LogicRet.Success;
    }
  }

  [Invoke((long)ThingNameEnum.JingDeng)]
  public class UseJingDengInvoker : AInvokeHandler<UseThingStruct, ETTask<LogicRet>>
  {
    public override async ETTask<LogicRet> Handle(UseThingStruct args)
    {
      User user = args.user;
      MapNode mapNode = args.map;
      StringBuilder sb = new StringBuilder();
      int cnt = 0;
      List<MapNode> mapNodes = MapProcSystem.GetMapNodesFromCache(mapNode.mapName);
      foreach (MapNode oneNode in mapNodes)
      {
        foreach (UserInMap oneUserInMap in oneNode.userInPoint.Values)
        {
          if (oneUserInMap.userStates.Contains(UserStateEnum.Evil_State))
          {
            ++cnt;
            sb.Append("<u>").Append(oneUserInMap.name).Append("</u>在<u>").Append(oneNode.mapName).Append("/")
                .Append(oneNode.pointName).Append("</u>，");
          }
        }
      }
      if (cnt == 0)
      {
        return LogicRet.Failed("当前地区没有犯罪人员");
      }
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      bagComponent.AddThingNumWithSend(args.thing, -1);
      user.SendChat("探测到" + cnt + "名犯罪人员:" + sb.ToString());
      user.SendToast("探测到" + cnt + "名犯罪人员，请尽快前往抓捕！");
      await ETTask.CompletedTask;
      return LogicRet.Success;
    }
  }

  [Invoke((long)ThingNameEnum.XiaoHeiWu_TeSheLing)]
  public class UseXiaoHeiWuTeSheLingInvoker : AInvokeHandler<UseThingStruct, ETTask<LogicRet>>
  {
    public override async ETTask<LogicRet> Handle(UseThingStruct args)
    {
      User user = args.user;
      if (user.GetComponent<MoveComponent>().nowMap != "小黑屋")
      {
        return LogicRet.Failed("必须在小黑屋中使用！");
      }
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      user.AddEvilNum(-100);
      bagComponent.AddThingNumWithSend(args.thing, -1);
      user.SendToast("使用小黑屋特赦令成功！");
      await ETTask.CompletedTask;
      return LogicRet.Success;
    }
  }

  [Invoke((long)ThingNameEnum.ZhaoHun_BaiFan)]
  public class UseZhaoHunBaiFanInvoker : AInvokeHandler<UseThingStruct, ETTask<LogicRet>>
  {
    public override async ETTask<LogicRet> Handle(UseThingStruct args)
    {
      User user = args.user;
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      if (attackComponent.LiveState != LiveStateEnum.DEAD)
      {
        return LogicRet.Failed("您当前状态无法使用招魂白幡！");
      }
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      bagComponent.AddThingNumWithSend(args.thing, -1);
      user.ReviveUser(attackComponent.maxBlood / 20, attackComponent.maxBlue / 20);
      MapNode mapNode = args.map;
      mapNode.SendMessageToMapUser(new ServerSendChatMsg
      {
        content = $"玩家<u>{user.nickname}</u>使用招魂白幡复活了！",
        chatType = ChatType.Local_Chat
      });
      user.SendToast("使用招魂白幡成功！");
      await ETTask.CompletedTask;
      return LogicRet.Success;
    }
  }

  [Invoke((long)ThingNameEnum.HunPo_LingShi)]
  public class UseHunPoLingShiInvoker : AInvokeHandler<UseThingStruct, ETTask<LogicRet>>
  {
    public override async ETTask<LogicRet> Handle(UseThingStruct args)
    {
      User user = args.user;
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      if (attackComponent.LiveState != LiveStateEnum.DEAD)
      {
        return LogicRet.Failed("您当前状态无法使用魂魄灵石！");
      }
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      bagComponent.AddThingNumWithSend(args.thing, -1);
      user.ReviveUser(attackComponent.maxBlood / 2, attackComponent.maxBlue / 2);
      MapNode mapNode = args.map;
      mapNode.SendMessageToMapUser(new ServerSendChatMsg
      {
        content = $"玩家<u>{user.nickname}</u>使用魂魄灵石复活了！",
        chatType = ChatType.Local_Chat
      });
      user.SendToast("使用魂魄灵石成功！");
      await ETTask.CompletedTask;
      return LogicRet.Success;
    }
  }

  [Invoke((long)ThingNameEnum.GaiMing_Ka)]
  public class UseGaiMingKaInvoker : AInvokeHandler<UseThingStruct, ETTask<LogicRet>>
  {
    public override async ETTask<LogicRet> Handle(UseThingStruct args)
    {
      User user = args.user;
      ClientUseThingMsg msg = args.msg;
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      if (msg.msg == null || msg.msg.Length == 0)
      {
        return LogicRet.Failed("请输入改名内容");
      }
      FiberUsersComponent fiberUsersComponent = args.map.Root().GetComponent<FiberUsersComponent>();
      if (!fiberUsersComponent.CheckNickName(msg.msg, out string msgStr))
      {
        return LogicRet.Failed(msgStr);
      }
      user.nickname = msg.msg;
      bagComponent.AddThingNumWithSend(args.thing, -1);
      user.SendMessage(ServerUpdatePartUserInfoMsg.Create(user, UserUpdateFlagEnum.NickName));
      user.SendToast("使用改名卡成功！");
      await ETTask.CompletedTask;
      return LogicRet.Success;
    }
  }
}