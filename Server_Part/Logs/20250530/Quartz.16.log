2025-05-30 16:29:08.0331 INFO [Quartz.Impl.StdSchedulerFactory] Default Quartz.NET properties loaded from embedded resource file 
2025-05-30 16:29:08.2163 DEBUG [Quartz.Simpl.TaskSchedulingThreadPool] TaskSchedulingThreadPool configured with max concurrency of 10 and TaskScheduler ThreadPoolTaskScheduler. 
2025-05-30 16:29:08.2181 INFO [Quartz.Core.SchedulerSignalerImpl] Initialized Scheduler Signaller of type: Quartz.Core.SchedulerSignalerImpl 
2025-05-30 16:29:08.2181 INFO [Quartz.Core.QuartzScheduler] Quartz Scheduler created 
2025-05-30 16:29:08.2181 INFO [Quartz.Simpl.RAMJobStore] RAMJobStore initialized. 
2025-05-30 16:29:08.2188 INFO [Quartz.Impl.StdSchedulerFactory] Quartz Scheduler 3.14.0.0 - 'DefaultQuartzScheduler' with instanceId 'NON_CLUSTERED' initialized 
2025-05-30 16:29:08.2188 INFO [Quartz.Impl.StdSchedulerFactory] Using thread pool 'Quartz.Simpl.DefaultThreadPool', size: 10 
2025-05-30 16:29:08.2188 INFO [Quartz.Impl.StdSchedulerFactory] Using job store 'Quartz.Simpl.RAMJobStore', supports persistence: False, clustered: False 
2025-05-30 16:29:08.2207 INFO [Quartz.Core.QuartzScheduler] Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED started. 
2025-05-30 16:29:08.2208 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 16:29:08.2981 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 16:29:08.2981 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 16:29:08.2981 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 16:29:08.2981 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 16:29:08.2981 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 16:29:08.2994 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 16:29:37.4500 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 16:30:02.5585 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 1 triggers 
2025-05-30 16:30:08.2983 DEBUG [Quartz.Simpl.SimpleJobFactory] Producing instance of Job 'global.Global_Biz_Per_1_Min', class=MaoYouJi.MaoScheduleJob 
2025-05-30 16:30:08.3022 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 16:30:08.3106 DEBUG [Quartz.Core.JobRunShell] Calling Execute on job global.Global_Biz_Per_1_Min 
2025-05-30 16:30:08.3156 DEBUG [Quartz.Core.JobRunShell] Trigger instruction : NoInstruction 
