using System;
using System.Collections.Generic;
using System.Globalization;

namespace MaoYouJi
{
  public class TimeInfo : Singleton<TimeInfo>, ISingletonAwake
  {
    private int timeZone;

    public int TimeZone
    {
      get
      {
        return this.timeZone;
      }
      set
      {
        this.timeZone = value;
        dt = dt1970.AddHours(TimeZone);
      }
    }

    private DateTime dt1970;
    private DateTime dt;

    // ping消息会设置该值，原子操作
    public long ServerMinusClientTime { private get; set; }

    public long FrameTime { get; private set; }

    public void Awake()
    {
      this.dt1970 = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
      this.dt = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
      this.FrameTime = this.ClientNow();
      this.TimeZone = 8;
    }

    public void Update()
    {
      // 赋值long型是原子操作，线程安全
      this.FrameTime = this.ClientNow();
    }

    /// <summary>
    /// 根据时间戳获取时间
    /// </summary>
    public DateTime ToDateTime(long timeStamp)
    {
      return dt.AddTicks(timeStamp * 10000);
    }

    public DateTime DelayDateTime(long timeStamp)
    {
      return dt1970.AddTicks((timeStamp + ServerNow()) * 10000);
    }

    // 线程安全
    public long ClientNow()
    {
      return (DateTime.UtcNow.Ticks - this.dt1970.Ticks) / 10000;
    }

    public long ServerNow()
    {
      return ClientNow() + this.ServerMinusClientTime;
    }

    public long ClientFrameTime()
    {
      return this.FrameTime;
    }

    public long ServerFrameTime()
    {
      return this.FrameTime + this.ServerMinusClientTime;
    }

    public long Transition(DateTime d)
    {
      return (d.Ticks - dt.Ticks) / 10000;
    }

    /// <summary>
    /// 将字符串时间（如"06:00"）转为今天对应的毫秒时间戳
    /// </summary>
    public long TodayTimeToTimestamp(string timeStr)
    {
      // 获取当前日期
      DateTime now = DateTime.UtcNow.AddHours(TimeZone);
      DateTime today = new DateTime(now.Year, now.Month, now.Day, 0, 0, 0, DateTimeKind.Utc);

      // 解析字符串时间
      var parts = timeStr.Split(':');
      int hour = int.Parse(parts[0]);
      int minute = int.Parse(parts[1]);

      // 构造今天的目标时间
      DateTime target = today.AddHours(hour).AddMinutes(minute);

      // 转为时间戳
      return Transition(target);
    }

    /// <summary>
    /// 从时间字符串列表中找到距离当前时间最近的下一个时间点
    /// </summary>
    /// <param name="times">时间字符串列表，格式如"06:00", "12:00", "18:00"</param>
    /// <returns>下一个时间点的Unix时间戳（毫秒级）</returns>
    public long GetNextTime(List<string> times)
    {
      if (times == null || times.Count == 0)
      {
        return 0; // 或者抛出异常，根据需求决定
      }

      long nowMillis = TimeInfo.Instance.ServerNow();
      List<long> nextTimestamps = new List<long>();

      foreach (string timeStr in times)
      {
        try
        {
          // 解析时间字符串（格式：HH:mm）
          if (!DateTime.TryParseExact(timeStr, "HH:mm", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime parsedTime))
          {
            ETLog.Error($"Invalid time format: {timeStr}");
            continue;
          }

          // 获取当前时间（考虑时区）
          DateTime now = DateTime.UtcNow.AddHours(TimeInfo.Instance.TimeZone);

          // 构造今天的目标时间
          DateTime todayTarget = new DateTime(now.Year, now.Month, now.Day, parsedTime.Hour, parsedTime.Minute, 0, DateTimeKind.Utc);

          // 转换为时间戳
          long todayMillis = TimeInfo.Instance.Transition(todayTarget);

          // 如果今天这个时间点还没过，加入候选列表（添加1分钟缓冲时间）
          if (todayMillis > (nowMillis + 60000)) // 60000毫秒 = 1分钟
          {
            nextTimestamps.Add(todayMillis);
          }

          // 计算明天这个时间点的时间戳，并加入候选列表
          DateTime tomorrowTarget = todayTarget.AddDays(1);
          long tomorrowMillis = TimeInfo.Instance.Transition(tomorrowTarget);
          nextTimestamps.Add(tomorrowMillis);
        }
        catch (Exception ex)
        {
          // 处理解析错误，例如打印日志或跳过无效格式
          ETLog.Error($"Error parsing time format: {timeStr}, Exception: {ex.Message}");
          // 可以选择继续处理下一个时间或返回错误码/抛出异常
        }
      }

      if (nextTimestamps.Count == 0)
      {
        // 如果所有时间都在今天之前，并且没有提供任何时间，这里需要处理
        // 这种情况理论上不应该发生，因为我们总是添加了明天的时间戳
        // 但为了健壮性，可以返回0或抛出异常
        return 0;
      }

      // 对所有可能的时间戳排序，找到最小的（即最近的）
      nextTimestamps.Sort();

      // 返回列表中第一个（即最近的）时间戳
      return nextTimestamps[0];
    }
  }
}