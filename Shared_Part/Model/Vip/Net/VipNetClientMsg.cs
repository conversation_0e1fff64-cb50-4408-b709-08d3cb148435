using MemoryPack;

namespace <PERSON><PERSON>ouJi
{
  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientEnterLianGongMsg)]
  public partial class ClientEnterLianGongMsg : MaoYouMessage, ILocationMessage
  {
    public bool isGaoji;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientQuitLianGongMsg)]
  public partial class ClientQuitLianGongMsg : MaoYouMessage, ILocationMessage
  {
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientChangeLianGongMsg)]
  public partial class ClientChangeLianGongMsg : MaoYouMessage, ILocationMessage
  {
    public int setType; // 设置类型，0：设置练功时间，1：设置练功技能
    public long setTime; // 设置的练功时间，单位分钟
    public int skillIdx; // 练功时使用的技能
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientEnterLvGuanMsg)]
  public partial class ClientEnterLvGuanMsg : MaoYouMessage, ILocationMessage
  {
    public bool isGaoji = false; // 是否高级旅馆
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientQuitLvGuanMsg)]
  public partial class ClientQuitLvGuanMsg : MaoYouMessage, ILocationMessage
  {
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientChangeLvGuanMsg)]
  public partial class ClientChangeLvGuanMsg : MaoYouMessage, ILocationMessage
  {
    public long setTime; // 设置的旅馆时间，单位分钟
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientEnterVfkMsg)]
  public partial class ClientEnterVfkMsg : MaoYouMessage, ILocationMessage
  {
    public AfkSystemType afkSystemType;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientQuitVfkMsg)]
  public partial class ClientQuitVfkMsg : MaoYouMessage, ILocationMessage
  {
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientChangeVfkMsg)]
  public partial class ClientChangeVfkMsg : MaoYouMessage, ILocationMessage
  {
    public long setTime; // 设置的挂机时间，单位分钟
  }
}