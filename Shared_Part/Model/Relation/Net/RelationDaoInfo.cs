using System.Collections.Generic;
using MemoryPack;
using MongoDB.Bson;

namespace MaoYouJi
{
  [EnableClass]
  [MemoryPackable]
  public partial class RelationInfo
  {
    // 关系等级：敌对：-3，仇恨：-2，厌恶：-1，普通：0，平淡：1，友好：2，尊敬：3，崇敬：4，崇拜：5
    [StaticField]
    public static string[] relationLevelTxtList = { "敌对", "仇恨", "厌恶", "普通", "平淡", "友好", "尊敬", "崇敬", "崇拜" };
    [StaticField]
    public static int[] relationExpList = { 12000, 6000, 3000, 3000, 3000, 6000, 12000, 24000, 48000 };
    // 关系类型

    public RelationTypeEnum relationType;
    // 关系等级：敌对：-3，仇恨：-2，厌恶：-1，普通：0，平淡：1，友好：2，尊敬：3，崇敬：4，崇拜：5
    public int level = 0;
    // 关系值
    public int relationExp = 0;
    // 关系值上限，达到上限后，关系等级提升
    public int relationMaxExp = 0;

    public static int GetRelationExp(int level)
    {
      if (level < -3 || level > 5)
      {
        ETLog.Error("关系等级错误：" + level);
        return 0;
      }
      return relationExpList[level + 3];
    }

    public static string GetRelationLevelTxt(int level)
    {
      if (level < -3 || level > 5)
      {
        ETLog.Error("关系等级错误：" + level);
        return "普通";
      }
      return relationLevelTxtList[level + 3];
    }
  }

  [EnableClass]
  public class FriendCnt
  {
    public int friendNum;
    public int studentNum;
    public int teacherNum;
    public int loverNum;
  }

  [EnableClass]
  [MemoryPackable]
  public partial class FriendInfoRecord
  {
    public long id;
    public long srcUserId; // 用户ID，只在师徒关系中有意义，这个位置只能放徒弟ID
    public long targetUserId; // 目标用户ID，只在师徒关系中使用, 这个位置只能放师傅ID
    public FriendTypeEnum friendType; // 好友类型
    public long createTime; // 添加时间
  }

  [EnableClass]
  [MemoryPackable]
  public partial class AddFriendInfo
  {
    [StaticField]
    public static Dictionary<AddFriendTypeEnum, FriendTypeEnum> AddFriendTypeToFriendTypeMap = new Dictionary<AddFriendTypeEnum, FriendTypeEnum>()
    {
      { AddFriendTypeEnum.AddFriend, FriendTypeEnum.Friend },
      { AddFriendTypeEnum.AddTeacher, FriendTypeEnum.Teacher },
      { AddFriendTypeEnum.AddStudent, FriendTypeEnum.Teacher },
      { AddFriendTypeEnum.AddLover, FriendTypeEnum.Lover },
    };

    public ObjectId id;
    public AddFriendTypeEnum addFriendType; // 添加好友的类型
    public long srcUserId; // 发情请求的用户ID
    public long targetUserId; // 目标用户ID
    public string addMsg; // 添加好友请求消息
    public long createTime; // 创建时间
  }
}