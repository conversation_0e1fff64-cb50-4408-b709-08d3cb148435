using MemoryPack;

namespace <PERSON><PERSON>ou<PERSON><PERSON>
{
  [MemoryPackable]
  [Message(MaoOuterMessageRange.GetAnnouceReq)]
  [ResponseType(nameof(GetAnnouceResp))]
  public partial class GetAnnouceReq : MaoYouInMessage, IGlobalRequest
  {
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.GetRankInfoReq)]
  [ResponseType(nameof(GetRankInfoResp))]
  public partial class GetRankInfoReq : MaoYouInMessage, IGlobalRequest
  {
  }
}