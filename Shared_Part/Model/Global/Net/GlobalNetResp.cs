using System.Collections.Generic;
using MemoryPack;

namespace <PERSON><PERSON>ou<PERSON><PERSON>
{
  [MemoryPackable]
  [Message(MaoOuterMessageRange.GetAnnouceResp)]
  public partial class GetAnnouceResp : MaoYouOutMessage, IGlobalResponse
  {
    public List<AnnouncementInfo> announcements;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.GetRankInfoResp)]
  public partial class GetRankInfoResp : MaoYouOutMessage, IGlobalResponse
  {
    public List<RankItem> rankItems;
    public long updateTime; // 更新时间
  }
}